// API Configuration
export const API_CONFIG = {
  BASE_URL: import.meta.env.VITE_API_URL || "http://localhost:3001/api",
  TIMEOUT: 10000,
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000,
} as const;

// API Endpoints
export const API_ENDPOINTS = {
  // Auth endpoints
  AUTH: {
    LOGIN: "/organizer/login",
    REGISTER: "/auth/register",
    REFRESH: "/auth/refresh",
    PROFILE: "/auth/profile",
    CHANGE_PASSWORD: "/auth/change-password",
    FORGOT_PASSWORD: "/auth/forgot-password",
    RESET_PASSWORD: "/auth/reset-password",
  },

  // Event endpoints
  EVENTS: {
    LIST: "/organizer/events",
    GET: (id: string) => `/events/${id}`,
    DELETE: (id: string) => `/events/${id}`,
    PUBLISH: (id: string) => `/events/${id}/publish`,
    CANCEL: (id: string) => `/events/${id}/cancel`,
    COMPLETE: (id: string) => `/events/${id}/complete`,
    DUPLICATE: (id: string) => `/events/${id}/duplicate`,
  },

  // Attendee endpoints
  ATTENDEES: {
    LIST: (eventId: string) => `/events/${eventId}/attendees`,
    CREATE: (eventId: string) => `/events/${eventId}/attendees`,
    GET: (id: string) => `/attendees/${id}`,
    UPDATE: (id: string) => `/attendees/${id}`,
    DELETE: (id: string) => `/attendees/${id}`,
    CHECK_IN: (id: string) => `/attendees/${id}/checkin`,
    UNDO_CHECK_IN: (id: string) => `/attendees/${id}/undo-checkin`,
    RESEND_QR: (id: string) => `/attendees/${id}/resend-qr-code`,
    IMPORT: (eventId: string) => `/events/${eventId}/attendees/import`,
    EXPORT: (eventId: string) => `/events/${eventId}/attendees/export`,
    SEND_QR_CODES: (eventId: string) =>
      `/events/${eventId}/attendees/send-qr-codes`,
  },

  // Check-in endpoints
  CHECKINS: {
    HISTORY: (eventId: string) => `/events/${eventId}/checkins`,
  },

  // QR Code endpoints
  QR: {
    CHECK: "/organizer/qr_check",
  },

  // Doorlist endpoints
  DOORLIST: {
    GET: (eventDateId: string) =>
      `/organizer/doorlist?event_date_id=${eventDateId}`,
  },
} as const;

// HTTP Status Codes
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  TOO_MANY_REQUESTS: 429,
  INTERNAL_SERVER_ERROR: 500,
} as const;

// Error Messages
export const ERROR_MESSAGES = {
  NETWORK_ERROR: "Network error. Please check your connection.",
  UNAUTHORIZED: "Your session has expired. Please log in again.",
  FORBIDDEN: "You don't have permission to perform this action.",
  NOT_FOUND: "The requested resource was not found.",
  VALIDATION_ERROR: "Please check your input and try again.",
  SERVER_ERROR: "Something went wrong on our end. Please try again later.",
  RATE_LIMITED: "Too many requests. Please wait before trying again.",
  UNKNOWN_ERROR: "An unexpected error occurred.",
} as const;
