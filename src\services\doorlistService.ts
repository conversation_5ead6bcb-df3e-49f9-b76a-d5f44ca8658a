import { ApiService } from "./apiService";
import { API_ENDPOINTS } from "../config/api";
import type { ApiResponse } from "../types";

// Doorlist item interface based on the API response structure
export interface DoorlistItem {
  key: string;
  value: string;
  item: {
    id: string;
    transaction_id: string;
    user_id: string;
    block_by_user_id: string | null;
    event_id: string;
    event_stage_id: string;
    event_stage_date_id: string;
    event_zone_detail_id: string;
    event_seat_id: string;
    seat_number: number;
    price: number;
    is_invoice: boolean;
    seat_code: string;
    mint_address: string;
    nft_transaction: string;
    nft_code_order: string;
    checked_time: number | null;
    date_use: number;
    created_at: string;
    updated_at: string;
    user: {
      id: string;
      name: string;
      last_name: string;
      phone: string;
      phone_prefix: string;
      email: string;
      birthday: string;
      gender: number;
      off: boolean;
      identity_card: string;
      identity_type: string;
      status: number;
      active: boolean;
      deleted: boolean;
      created_at: string;
      updated_at: string;
      code: string | null;
      is_admin: boolean;
      zipcode: string;
      id_country: number;
      id_province: number;
      role: string;
      company: string | null;
      attributes: Record<string, any>;
    };
    event: {
      id: string;
      name_vi: string;
      venue_vi: string;
      meta_title_vi: string;
      showdate_from: number;
      showdate_to: number;
      public_sale: number;
      public_sale_to: number;
      event_type: number;
    };
    name: string;
    event_date: {
      id: number;
      event_stage_id: number;
      event_id: string;
      show_date: number;
      show_from: number;
      show_to: number;
      public_sale: number;
      public_sale_to: number;
      short_desc_vi: string;
      short_desc_en: string;
      ticket_price_desc_vi: string;
      ticket_price_desc_en: string;
      zone_map: string;
      zone_price_image_vi: string;
      zone_price_image_en: string;
      service_fee: string;
      active: boolean;
      is_free_ticket: boolean;
      deleted: boolean;
      created_at: string;
      updated_at: string;
      allow_check_in: number;
      status: number;
      show_stage: number;
      salepoint_code: string | null;
      seat_type: number;
      ticket_type: string;
      email_note_vi: string;
      email_note_en: string;
      attributes: {
        width: string;
        height: string;
      };
      min_order: number;
      max_order: number;
      countdown: number;
      is_unlimited: boolean;
      email_poster_vi: string | null;
      email_poster_en: string | null;
      notify_email: string | null;
      content_vi: string | null;
      content_en: string | null;
    };
    zone_name: string;
    seat_name: string;
    row_code: string;
    cart_temp: {
      phone: string;
      email: string;
      timer_ticket: string;
      event_date_id: number;
    };
  };
  ordinal_number: number;
}

// Simplified attendee interface for the UI
export interface DoorlistAttendee {
  id: string;
  name: string;
  email: string;
  phone?: string;
  seatCode: string;
  zoneName: string;
  price: number;
  checkedIn: boolean;
  checkedInAt?: Date;
  nftCodeOrder: string;
}

export class DoorlistService {
  /**
   * Get doorlist for a specific event date
   */
  static async getDoorlist(eventDateId: string): Promise<DoorlistAttendee[]> {
    try {
      const response = await ApiService.request<ApiResponse<DoorlistItem[]>>(
        API_ENDPOINTS.DOORLIST.GET(eventDateId),
        "get"
      );
      
      if (response.status === 200 && response.data) {
        // Transform the API response to our simplified format
        return response.data.map((doorlistItem): DoorlistAttendee => ({
          id: doorlistItem.item.id,
          name: `${doorlistItem.item.user.name} ${doorlistItem.item.user.last_name}`.trim(),
          email: doorlistItem.item.user.email,
          phone: doorlistItem.item.user.phone || undefined,
          seatCode: doorlistItem.item.seat_code,
          zoneName: doorlistItem.item.zone_name,
          price: doorlistItem.item.price,
          checkedIn: doorlistItem.item.checked_time !== null && doorlistItem.item.checked_time > 0,
          checkedInAt: doorlistItem.item.checked_time && doorlistItem.item.checked_time > 0 
            ? new Date(doorlistItem.item.checked_time * 1000) 
            : undefined,
          nftCodeOrder: doorlistItem.item.nft_code_order,
        }));
      }
      
      throw new Error(response.message || "Failed to fetch doorlist");
    } catch (error) {
      console.error("Get doorlist error:", error);
      throw error;
    }
  }
}
